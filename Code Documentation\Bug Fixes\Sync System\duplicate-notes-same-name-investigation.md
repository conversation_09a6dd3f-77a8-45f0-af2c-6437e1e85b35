# Duplicate Notes with Same Name - Sync System Investigation

## Issue Description

When two notes have the same name but different IDs and content, only one note gets saved to the backup during sync operations. Even when both notes contain different data, they are not both preserved in the sync backup, which causes data loss.

## Root Cause Analysis

After comprehensive investigation of the sync system codebase, I've identified the **exact cause** of this issue. The problem lies in a **fundamental mismatch between two different file naming systems** used in the sync process:

### The Two Conflicting Systems

#### 1. Manifest Manager Path Generation (manifest-manager.ts)
- **Location**: Lines 394-406 in `generateManifestFromDatabase()`
- **Logic**: Uses `sanitizeName()` method with collision detection
- **Collision Handling**: Automatically appends `_2`, `_3`, etc. for duplicate names
- **Result**: Notes with same name get different paths like:
  - `MyNote.noti` (first note)
  - `MyNote_2.noti` (second note with same name)

#### 2. Unified Sync Engine Export (unified-sync-engine.ts)  
- **Location**: Lines 1229, 1235, 1239 in `exportNote()`
- **Logic**: Uses `sanitizeNoteTitle()` directly without collision detection
- **Collision Handling**: **NONE** - always generates same filename for same title
- **Result**: Notes with same name get identical paths like:
  - `MyNote.noti` (both notes get this same path)

### The Critical Problem

1. **Manifest Generation**: When `generateManifestFromDatabase()` runs, it correctly creates unique paths:
   ```
   Note ID 1 (title: "MyNote") → path: "MyNote.noti"
   Note ID 2 (title: "MyNote") → path: "MyNote_2.noti"
   ```

2. **File Export**: When `exportNote()` runs, it ignores the manifest paths and regenerates filenames:
   ```
   Note ID 1 (title: "MyNote") → exports to: "MyNote.noti"
   Note ID 2 (title: "MyNote") → exports to: "MyNote.noti" (OVERWRITES!)
   ```

3. **File Overwrite**: The second note overwrites the first note's file because they have identical filenames.

## Code Evidence

### Manifest Manager (Correct Collision Handling)
```typescript
// Lines 394-406 in manifest-manager.ts
const noteTitle = this.sanitizeName(note.title, noteId, folderPath);
notePath = `${folderPath}${noteTitle}.noti`;

// sanitizeName() method handles collisions:
if (existingIds.size > 0) {
  let counter = 2;
  let newName = `${sanitized}_${counter}`;
  // ... finds unique name
}
```

### Unified Sync Engine (Missing Collision Handling)
```typescript
// Lines 1229, 1235, 1239 in unified-sync-engine.ts
notePath = path.join(directory, folderItem.path, `${sanitizeNoteTitle(note.title)}.noti`);
notePath = path.join(orphanedPath, `${sanitizeNoteTitle(note.title)}.noti`);
notePath = path.join(directory, `${sanitizeNoteTitle(note.title)}.noti`);
```

**Problem**: `sanitizeNoteTitle()` only sanitizes characters but doesn't handle name collisions.

## Why This Happens

1. **Design Inconsistency**: Two different parts of the system use different naming strategies
2. **Missing Path Reuse**: Export process doesn't use the collision-safe paths from the manifest
3. **File Overwriting**: `writeNote()` overwrites existing files without checking for conflicts

## Impact Assessment

- **Data Loss**: Second note with same name completely overwrites the first
- **Silent Failure**: No error messages or warnings to user
- **Backup Corruption**: Sync backup doesn't contain all user data
- **Cross-Device Issues**: Other devices won't receive the overwritten note

## Validation of Findings

The investigation confirms this is a **definitive bug** in the sync system architecture where:
1. Manifest correctly plans unique paths for duplicate names
2. Export process ignores those paths and creates collisions
3. File system overwrites occur silently
4. User data is permanently lost from backup

This explains exactly why changing the name of the second note fixes the issue - it breaks the collision and allows both notes to be exported with different filenames.

## Technical Solution Overview

The fix requires making the export process respect the collision-safe paths already generated by the manifest manager:

### Option 1: Use Manifest Paths (Recommended)
- Modify `exportNote()` to use `item.path` from manifest instead of regenerating paths
- Ensures consistency between manifest and actual file locations
- Preserves existing collision detection logic

### Option 2: Add Collision Detection to Export
- Implement collision detection in `exportNote()` similar to manifest manager
- More complex and duplicates existing logic
- Risk of inconsistency between systems

## Files That Need Modification

1. **Primary Fix**: `electron/main/api/sync-logic/unified-sync-engine.ts`
   - Lines 1229, 1235, 1239 in `exportNote()` method
   - Replace path generation with manifest path lookup

2. **Verification**: `electron/main/api/sync-logic/manifest-manager.ts`
   - Ensure collision detection logic is robust
   - Verify path generation consistency

## Testing Strategy

1. Create two notes with identical names but different content
2. Trigger sync operation
3. Verify both notes exist in backup directory with unique filenames
4. Verify both notes can be imported correctly on other devices
5. Test with notes in folders, books, and root level

## Priority Level: CRITICAL

This is a **data loss bug** that silently corrupts user backups. It should be fixed immediately to prevent permanent loss of user content during sync operations.
