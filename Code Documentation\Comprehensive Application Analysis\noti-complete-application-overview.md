# Noti Application - Complete Comprehensive Overview

## Executive Summary

Noti is a sophisticated desktop note-taking application built with Electron, Vue 3, and TypeScript. It combines powerful note management with book tracking, folder organization, Pomodoro timer functionality, and advanced synchronization capabilities. The application features a rich-text editor powered by TipTap, comprehensive export options, and a modern, theme-aware user interface designed for productivity and cross-device synchronization.

## Core Architecture

### Technology Stack
- **Frontend**: Vue 3 with Composition API, TypeScript, TipTap rich-text editor
- **Backend**: Electron main process with SQLite database
- **Build System**: Vite with electron-builder for cross-platform distribution
- **Communication**: IPC (Inter-Process Communication) bridge with type-safe API layer
- **Styling**: CSS custom properties with comprehensive theme system
- **State Management**: Pinia stores for reactive state management

### Application Structure
The application follows a clean separation between the Electron main process (backend) and the Vue renderer process (frontend), connected through a robust IPC layer that provides type-safe database operations and system integrations.

## Database Architecture

### Core Database Schema
The application uses a sophisticated SQLite database with 15 interconnected tables supporting hierarchical data organization:

**Primary Entity Tables:**
- **notes**: Stores note content with both plain text and HTML versions, supporting folder and book associations
- **folders**: Hierarchical folder structure with self-referencing parent_id relationships
- **books**: Complete book metadata including OpenLibrary integration, reading progress, and ratings
- **media_files**: Embedded media handling with file path management and cover image support

**Functional Tables:**
- **timer_sessions**: Pomodoro timer sessions with duration tracking and productivity analytics
- **pomodoro_cycles**: Individual pomodoro cycles within timer sessions
- **recent_items**: Activity tracking for quick access to recently used content
- **settings**: Application configuration with JSON value storage
- **sync_state**: Synchronization state tracking with hash-based change detection
- **exports**: Export history and file path tracking

**Relationship Design:**
- Foreign key constraints with CASCADE and SET NULL behaviors for data integrity
- Hierarchical structures supporting unlimited nesting depth for folders
- Many-to-many relationships between notes, folders, and books
- Audit trails for backup and synchronization operations

## Note Management System

### Rich Text Editor (TipTap Integration)
The note editor is built on TipTap, providing a comprehensive rich-text editing experience:

**Core Features:**
- **Text Formatting**: Bold, italic, underline, strikethrough, subscript, superscript
- **Typography**: Multiple font families, text colors, highlighting, text alignment
- **Structure**: Headings (H1-H6), paragraphs, lists (ordered/unordered), task lists with checkboxes
- **Advanced Elements**: Tables with resizing, links with validation, code blocks with syntax highlighting
- **Media Support**: Image insertion with drag-and-drop, clipboard paste, and external URL processing

**Media Handling:**
- Automatic image processing from external URLs with local storage
- Base64 embedding for sync compatibility
- Custom media protocol (noti-media://) for secure file access
- Drag-and-drop file upload with automatic media file management
- Image resizing and optimization to prevent layout issues

**Content Storage:**
- Dual storage format: HTML content for rich display, plain text for search and compatibility
- Real-time content synchronization between editor and database
- Automatic save functionality with visual feedback
- Word and character count tracking

### Note Organization
**Hierarchical Structure:**
- Notes can belong to folders, books, or exist as standalone items
- Unlimited folder nesting with visual breadcrumb navigation
- Color-coding system for visual organization and quick identification
- Custom ordering within containers with drag-and-drop reordering

**Metadata Management:**
- Creation and modification timestamps with timezone handling
- Last viewed tracking for recent items functionality
- Type classification system for different note categories
- Custom fields support for extensible metadata

## Book Management System

### OpenLibrary Integration
The application features deep integration with OpenLibrary API for comprehensive book data:

**Book Discovery:**
- Advanced search with fuzzy matching and query preprocessing
- Multiple search strategies: title, author, ISBN, and combined queries
- Automatic cover image downloading with fallback mechanisms
- Real-time search results with pagination and filtering

**Book Data Management:**
- Complete metadata storage: title, author, ISBN, publication date, description
- Genre classification and language detection
- Page count and reading progress tracking
- Personal rating system and custom fields for user annotations

**Reading Progress:**
- Current page tracking with percentage completion calculation
- Reading status management (to-read, reading, completed, abandoned)
- Integration with timer sessions for reading time tracking
- Progress visualization and reading statistics

### Book-Note Integration
**Automatic Linking:**
- Smart note-to-book association based on content analysis
- Folder creation for each book with automatic organization
- Batch linking operations for existing notes
- Context-aware note suggestions when viewing books

## Folder Management System

### Hierarchical Organization
**Structure Design:**
- Self-referencing folder hierarchy with unlimited nesting depth
- Special "Books" root folder that's immutable and essential for application functionality
- Parent-child relationships with CASCADE deletion for child folders
- Automatic folder creation for books with customizable naming patterns

**Folder Operations:**
- Create, rename, delete, and move operations with validation
- Bulk operations for multiple folder management
- Color-coding system for visual organization
- Note count tracking and display for each folder

**Navigation System:**
- Tree-view navigation with expand/collapse functionality
- Breadcrumb navigation for deep folder structures
- Quick access to recently used folders
- Search functionality across folder names and contents

## Timer and Productivity System

### Pomodoro Timer Implementation
**Core Timer Functionality:**
- Configurable work and break intervals (default: 25/5 minutes)
- Visual and audio notifications for session transitions
- Pause, resume, and stop functionality with state persistence
- Background operation with system tray integration

**Session Management:**
- Named sessions with category classification
- Focus area specification for targeted productivity tracking
- Automatic session logging with duration and completion status
- Integration with Discord Rich Presence for activity sharing

**Productivity Analytics:**
- Daily, weekly, and monthly productivity statistics
- Focus time tracking with category breakdown
- Session completion rates and productivity trends
- Visual charts and graphs for progress visualization

### Timer-Note Integration
- Automatic note creation for timer sessions
- Context linking between timer sessions and active notes/books
- Productivity insights based on content creation during timer sessions

## Synchronization System

### Architecture Overview
The sync system is a sophisticated bidirectional synchronization engine that maintains consistency between local SQLite database and file system backups:

**Core Components:**
- **Unified Sync Engine**: Main orchestrator handling the complete sync process
- **Manifest Manager**: Manages .sync-manifest.json files for state tracking
- **Change Detector**: Hash-based change detection between local and remote states
- **File Operations**: Atomic file I/O with validation and error handling
- **Conflict Resolver**: Handles conflicts when items are modified on multiple devices

### Sync Process Flow
**Initialization Phase:**
1. Load existing sync manifest from target directory
2. Validate sync environment and acquire sync lock
3. Initialize device identification and metadata

**Change Detection:**
1. Generate current database state manifest
2. Compare with remote manifest using hash-based detection
3. Identify items to import, export, and resolve conflicts
4. Process pending deletions from previous sync operations

**Hierarchical Processing:**
1. Process books first (create folders and metadata)
2. Process folders second (maintain hierarchy)
3. Process notes last (with proper parent associations)
4. Handle media files and embedded content

**Conflict Resolution:**
- Timestamp-based conflict resolution with user override options
- Backup creation for conflicted items
- Merge strategies for non-conflicting changes

### File System Structure
```
sync-directory/
├── .sync-manifest.json          # Central manifest file
├── Books/                       # Books directory (immutable)
│   ├── BookTitle1/
│   │   ├── .cover.jpg          # Hidden cover image
│   │   ├── FolderName/
│   │   │   └── note.noti       # Note in .noti format
│   │   └── note.noti           # Note in book root
│   └── BookTitle2/
└── standalone-note.noti         # Root level note
```

### Sync Features
**Multi-Device Support:**
- Device identification and tracking
- Automatic bidirectional sync on application startup
- Real-time change detection with database hooks
- Cloud storage compatibility (Google Drive, OneDrive, Dropbox)

**Data Integrity:**
- Atomic operations to prevent corruption
- Hash-based change detection for performance
- Deletion tracking with manifest-based recovery
- Path validation to prevent directory traversal attacks

## Export System

### Export Formats
**Supported Formats:**
- **PDF**: High-quality PDF generation with formatting preservation
- **Markdown**: Clean markdown export with media embedding
- **Noti Format**: Native format with complete metadata preservation
- **HTML**: Standalone HTML files with embedded styles

**Export Capabilities:**
- Single note export with format selection
- Bulk export for multiple notes and folders
- Recursive folder export with structure preservation
- Media file embedding and external linking options

**Export Features:**
- Custom file naming patterns
- Directory structure preservation
- Metadata inclusion options
- Progress tracking for large export operations

## User Interface Architecture

### Vue 3 Component Structure
**Core Layout Components:**
- **App.vue**: Root component with theme management and initialization
- **TitleBar.vue**: Custom window controls with navigation integration
- **SidebarNavigation.vue**: Main navigation with collapsible design and quick access

**Feature-Specific Views:**
- **NotesView.vue**: Note management with editor integration and modal system
- **BooksView.vue**: Book collection with search and OpenLibrary integration
- **FoldersView.vue**: Hierarchical folder management with tree navigation
- **TimerView.vue**: Pomodoro timer with session tracking and analytics
- **DashboardView.vue**: Overview dashboard with recent items and statistics
- **SettingsView.vue**: Application configuration with theme and sync settings

### Modal System Architecture
**Design Pattern:**
- Parent-wrapped modal architecture where views handle overlays with blur effects
- Modals rely on parent overlay blur instead of implementing their own
- Consistent modal behavior across the application
- Type-safe modal communication with event emission

**Modal Components:**
- Note management modals (create, edit, delete, export)
- Book management modals (add, edit, search, link)
- Folder management modals (create, rename, delete, move)
- Settings and configuration modals
- Import/export progress modals

### Theme System
**Architecture:**
- CSS custom properties with 200+ variables covering all UI elements
- Complete light and dark theme definitions
- System theme detection with automatic switching
- Smooth theme transitions with animation support

**Theme Features:**
- Light, dark, and system preference modes
- Mobile browser chrome color updates
- Component-level theme awareness
- Extensible architecture for future theme additions

## Performance Optimizations

### Database Performance
- Comprehensive indexing strategy for fast queries
- Connection pooling and prepared statements
- Lazy loading for large datasets
- Efficient foreign key constraint handling

### UI Performance
- Component lazy loading and code splitting
- Virtual scrolling for large lists
- Debounced search and input handling
- Optimized re-rendering with Vue 3 reactivity

### Sync Performance
- Hash-based change detection to avoid full database scans
- Incremental sync with only changed items
- Parallel processing for independent operations
- Efficient file I/O with atomic operations

## Security Features

### Data Protection
- Path validation to prevent directory traversal attacks
- Input sanitization for all user-provided data
- Secure media file handling with custom protocol
- Database transaction isolation for data integrity

### Sync Security
- Sync mutex to prevent concurrent operations
- Atomic file operations to prevent corruption
- Validation of manifest and file structures
- Secure handling of external image URLs

## Development and Build System

### Development Workflow
- Hot module replacement for rapid development
- TypeScript strict mode for type safety
- ESLint and Prettier for code quality
- Comprehensive error handling and logging

### Build and Distribution
- Cross-platform builds for Windows (.exe, .msix), macOS (.dmg), and Linux
- Electron-builder configuration for native installers
- Code signing and notarization support
- Automatic update system integration

### Testing Infrastructure
- Unit tests for core functionality
- Integration tests for database operations
- End-to-end tests for user workflows
- Performance testing for sync operations

## Future Extensibility

The application architecture is designed for extensibility with:
- Plugin system architecture for custom extensions
- Theme system ready for additional themes
- Export system extensible for new formats
- Sync system compatible with additional cloud providers
- Database schema migration system for future updates

This comprehensive overview demonstrates that Noti is a sophisticated, well-architected application that combines powerful note-taking capabilities with advanced book management, productivity tools, and robust synchronization features, all wrapped in a modern, user-friendly interface designed for cross-platform productivity.
