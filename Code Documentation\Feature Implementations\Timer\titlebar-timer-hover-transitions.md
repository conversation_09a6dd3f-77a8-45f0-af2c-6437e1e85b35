# Titlebar Timer Hover Transitions

## Files Modified
- `src/components/common/TitleBar.vue`

## What Was Done
Implemented smooth hover transitions for the timer container in the titlebar that changes the entire button content when hovering.

## How It Was Implemented

### Behavior Requirements
1. **Running Timer + Hover**: Shows "Pause" text across entire button
2. **Running Timer + Hover Away**: Reverts to time display with pulsing dot
3. **Running Timer + Click**: Pauses timer, shows paused state with pause icon
4. **Paused Timer + Hover**: Shows "Resume" text across entire button  
5. **Paused Timer + Hover Away**: Reverts to paused time with pause icon
6. **Paused Timer + Click**: Resumes timer, shows running state with pulsing dot

### Technical Implementation

#### Template Changes
- Added separate transitions for icon and text content
- Icon only shows when not hovering (`v-if="!isHoveringTimer"`)
- Text content switches between action text (Pause/Resume) and time display
- Wrapped time and timer type in a `timer-content` container for better layout

#### CSS Improvements
- Increased transition duration to 0.25s for smoother animations
- Added `min-width` and `justify-content: center` for consistent sizing
- Added `timer-hovering` class for hover state styling
- Separate transition animations for icon and text:
  - Icon: scale and opacity transitions
  - Text: horizontal slide transitions (translateX)
- Added `flex-shrink: 0` to prevent icon compression

#### Key Features
- Smooth transitions between states
- Consistent button width during transitions
- Proper icon animations (pulsing dot when running)
- Clean hover effects with subtle background changes
- Maintains existing functionality while improving UX

## Testing Notes
- Test with timer running and paused states
- Verify smooth transitions on hover in/out
- Confirm click functionality works in both states
- Check that pulsing animation works when timer is running
