/* Theme System for Noti Application */

/* ===== LIGHT THEME (DEFAULT) ===== */
:root,
.theme-light {
  /* Primary Colors */
  --color-primary: #333333;
  --color-primary-hover: #1a1a1a;
  --color-primary-light: #555555;
  
  /* Background Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f5f5f5;
  --color-bg-tertiary: #f8f8f8;
  --color-bg-elevated: #ffffff;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --color-text-primary: #333333;
  --color-text-secondary: #555555;
  --color-text-tertiary: #777777;
  --color-text-muted: #999999;
  --color-text-inverse: #ffffff;
  
  /* Border Colors */
  --color-border-primary: #e3e3e3;
  --color-border-secondary: #eaeaea;
  --color-border-focus: #333333;
  --color-border-hover: #cccccc;

  /* Button Colors */
  --color-btn-primary-bg: #333333;
  --color-btn-primary-text: #ffffff;
  --color-btn-primary-hover: #1a1a1a;
  --color-btn-secondary-bg: #D9D9D9;
  --color-btn-secondary-text: #333333;
  --color-btn-secondary-hover: #C9C9C9;
  --color-btn-secondary-border: #e3e3e3;
  
  /* Input Colors */
  --color-input-bg: #ffffff;
  --color-input-border: #e3e3e3;
  --color-input-focus: #333333;
  --color-input-text: #333333;
  --color-input-placeholder: #999999;
  
  /* Card Colors */
  --color-card-bg: #ffffff;
  --color-card-border: #e3e3e3;
  --color-card-shadow: rgba(0, 0, 0, 0.1);
  --color-card-hover-bg: #f8f8f8;
  --color-card-hover-shadow: rgba(0, 0, 0, 0.15);

  /* Code Colors */
  --color-code-bg: #f3f3f3;
  --color-code-block-bg: #f6f8fa;
  --color-code-text: #333333;
  
  /* Navigation Colors */
  --color-nav-bg: #ffffff;
  --color-nav-border: #eaeaea;
  --color-nav-item-hover: #f5f5f5;
  --color-nav-item-active: rgba(51, 51, 51, 0.1);
  
  /* Modal Colors */
  --color-modal-bg: #ffffff;
  --color-modal-overlay: rgba(0, 0, 0, 0.5);
  --color-modal-border: #e3e3e3;
  
  /* Status Colors */
  --color-success: #38A169;
  --color-warning: #FF8C00;
  --color-error: #E53E3E;
  --color-info: #3182CE;

  /* Status Color Variants */
  --color-success-text: #22c55e;
  --color-success-bg: rgba(34, 197, 94, 0.1);
  --color-error-text: #ef4444;
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-warning-text: #f59e0b;
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-info-text: #3b82f6;
  --color-info-bg: rgba(59, 130, 246, 0.1);
  
  /* Timer Colors */
  --color-timer-bg: #ffffff;
  --color-timer-border: #e3e3e3;
  --color-timer-button-active: rgba(217, 217, 217, 1);
  --color-timer-button-outlined: rgba(255, 255, 255, 1);
  
  /* Scrollbar Colors */
  --color-scrollbar-track: #f1f1f1;
  --color-scrollbar-thumb: #d1d1d1;
  --color-scrollbar-thumb-hover: #bbbbbb;

  /* Folder/Item Count Colors */
  --color-item-count-bg: #f0f0f0;
  --color-item-count-text: #777;

  /* Table and Content Colors */
  --color-table-cell: #777777;
  --color-table-border: #e0e0e0;
  --color-table-header-bg: #f9f9f9;
  --color-empty-state: #777;
  --color-loading-text: #777777;

  /* Create/Action Button Colors */
  --color-create-btn-bg: #777777;
  --color-create-btn-hover: #5a5a5a;
  --color-action-hover: rgba(0, 0, 0, 0.05);
  --color-action-danger-hover: rgba(231, 76, 60, 0.1);

  /* Breadcrumb Colors */
  --color-breadcrumb-text: #555;
  --color-breadcrumb-hover: #f0f0f0;
  --color-breadcrumb-current: #333;
  --color-breadcrumb-truncated: #888;
  --color-breadcrumb-truncated-bg: #e9ecef;
  --color-breadcrumb-truncated-hover: #e0e0e0;
  --color-breadcrumb-separator: #888;

  /* Delete/Danger Button Colors */
  --color-delete-btn-bg: #F5A6A6;
  --color-delete-btn-text: #d32f2f;
  --color-delete-btn-hover: #f48c8c;
  --color-delete-btn-disabled-bg: #e0e0e0;
  --color-delete-btn-disabled-text: #999;

  /* Export/Progress Modal Colors */
  --color-export-overlay: rgba(0, 0, 0, 0.6);
  --color-export-modal-bg: #ffffff;
  --color-export-spinner-track: #f3f3f3;
  --color-export-spinner-active: #333333;
  --color-export-title: #333333;
  --color-export-message: #777777;
  --color-export-details: #999999;

  /* Path Dropdown Colors */
  --color-path-dropdown-bg: #ffffff;
  --color-path-dropdown-border: #e3e3e3;
  --color-path-dropdown-shadow: rgba(0, 0, 0, 0.15);
  --color-path-dropdown-item: #555;
  --color-path-dropdown-hover: #f5f5f5;
  --color-path-dropdown-hover-text: #333;
  --color-path-dropdown-current: #333333;
  --color-path-dropdown-current-bg: #f5f5f5;

  /* Folder Action Colors */
  --color-folder-action-bg: #f5f5f5;
  --color-folder-action-hover: #e9e9e9;
  --color-folder-action-delete: #e74c3c;
  --color-folder-action-delete-hover: #ffebee;

  /* Folder Notes Colors */
  --color-folder-notes-title: #333;
  --color-folder-notes-desc: #777;

  /* Modal Overlay and Content */
  --color-modal-overlay-alt: rgba(0, 0, 0, 0.3);
  --color-modal-content-bg: #ffffff;
  --color-modal-content-shadow: rgba(0, 0, 0, 0.15);
  --color-modal-title-alt: #333;

  /* Button Ripple Effect */
  --color-btn-ripple: rgba(255, 255, 255, 0.4);

  /* Form Input Colors */
  --color-form-input-border: #d9d9d9;
  --color-form-input-text: #333;

  /* Warning Colors */
  --color-warning-bg: #fff8e1;
  --color-warning-border: #ffc107;

  /* Dashboard Colors */
  --color-dashboard-date: #666666;
  --color-dashboard-stat-label: #666666;
  --color-dashboard-stat-border: #f0f0f0;
  --color-dashboard-stat-shadow: rgba(0, 0, 0, 0.05);
  --color-dashboard-empty-bg: #f9f9f9;
  --color-dashboard-empty-text: #888888;
  --color-dashboard-chart-primary: #333333;
  --color-dashboard-chart-secondary: #666666;
  --color-dashboard-chart-tertiary: #888888;
  --color-dashboard-chart-grid: #eaeaea;
  --color-dashboard-action-icon-bg: #f5f5f5;
  --color-dashboard-action-icon-border: #e3e3e3;

  /* SVG Icon Colors */
  --color-icon-fill: #333333;
  --icon-filter: brightness(0) saturate(100%) invert(29%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(95%);

  /* Trash Button Colors */
  --color-trash-btn-bg: #F5A6A6;
  --color-trash-btn-hover: #f29191;
  --color-trash-btn-disabled-bg: #e0e0e0;

  /* Export Progress Modal Colors */
  --color-export-progress-overlay: rgba(0, 0, 0, 0.6);
  --color-export-progress-spinner-track: #f3f3f3;
  --color-export-progress-spinner-active: #333333;
  --color-export-progress-title: #333333;
  --color-export-progress-message: #777777;
  --color-export-progress-details: #999999;

  /* Window Control Colors */
  --color-window-close-hover: #e81123;
  --color-window-close-text: #ffffff;

  /* Wave Animation Colors */
  --color-wave-primary: rgba(51, 51, 51, 0.3);
  --color-wave-secondary: rgba(51, 51, 51, 0.6);
  --color-wave-tertiary: rgba(51, 51, 51, 0.2);

  /* Chart Fallback Colors */
  --color-chart-primary-fallback: rgba(51, 51, 51, 0.8);
  --color-chart-primary-border-fallback: rgba(51, 51, 51, 1);
  --color-chart-secondary-fallback: rgba(136, 136, 136, 0.6);
  --color-chart-secondary-border-fallback: rgba(136, 136, 136, 1);
  --color-chart-text-fallback: #333333;
  --color-chart-tertiary-text-fallback: #888;
  --color-chart-tooltip-bg-fallback: rgba(0, 0, 0, 0.8);
  --color-chart-tooltip-text-fallback: #fff;
  --color-chart-border-fallback: rgba(200, 200, 200, 0.3);

  /* Single Folder Component Colors */
  --color-folder-active-bg: #e7f1ff;
  --color-folder-hover-bg: #f0f4f8;
  --color-folder-breadcrumb-bg: #f5f7f9;
  --color-folder-breadcrumb-text: #0d6efd;
  --color-folder-breadcrumb-current: #212529;
  --color-folder-breadcrumb-truncated: #6c757d;
  --color-folder-breadcrumb-hover: #e9ecef;
  --color-folder-breadcrumb-hover-alt: #dee2e6;
  --color-folder-breadcrumb-separator: #6c757d;
  --color-folder-rename-border: #4a90e2;
  --color-folder-rename-bg: white;
  --color-folder-count-bg: #e9ecef;
  --color-folder-count-text: #495057;
}

/* ===== DARK THEME ===== */
.theme-dark {
  /* Primary Colors */
  --color-primary: #E0E0E0;
  --color-primary-hover: #ffffff;
  --color-primary-light: #cccccc;
  
  /* Background Colors */
  --color-bg-primary: #1e1e1e;
  --color-bg-secondary: #121212;
  --color-bg-tertiary: #262626;
  --color-bg-elevated: #1e1e1e;
  --color-bg-overlay: rgba(0, 0, 0, 0.7);
  
  /* Text Colors */
  --color-text-primary: #E0E0E0;
  --color-text-secondary: #cccccc;
  --color-text-tertiary: #aaaaaa;
  --color-text-muted: #888888;
  --color-text-inverse: #121212;
  
  /* Border Colors */
  --color-border-primary: #333333;
  --color-border-secondary: #2a2a2a;
  --color-border-focus: #E0E0E0;
  --color-border-hover: #444444;
  
  /* Button Colors */
  --color-btn-primary-bg: #E0E0E0;
  --color-btn-primary-text: #121212;
  --color-btn-primary-hover: #ffffff;
  --color-btn-secondary-bg: #262626;
  --color-btn-secondary-text: #E0E0E0;
  --color-btn-secondary-hover: #333333;
  --color-btn-secondary-border: #333333;
  
  /* Input Colors */
  --color-input-bg: #1e1e1e;
  --color-input-border: #333333;
  --color-input-focus: #E0E0E0;
  --color-input-text: #E0E0E0;
  --color-input-placeholder: #888888;
  
  /* Card Colors */
  --color-card-bg: #1e1e1e;
  --color-card-border: #333333;
  --color-card-shadow: rgba(0, 0, 0, 0.3);
  --color-card-hover-bg: #262626;
  --color-card-hover-shadow: rgba(0, 0, 0, 0.4);

  /* Code Colors */
  --color-code-bg: #2d2d2d;
  --color-code-block-bg: #262626;
  --color-code-text: #E0E0E0;
  
  /* Navigation Colors */
  --color-nav-bg: #1e1e1e;
  --color-nav-border: #333333;
  --color-nav-item-hover: #262626;
  --color-nav-item-active: rgba(224, 224, 224, 0.1);
  
  /* Modal Colors */
  --color-modal-bg: #1e1e1e;
  --color-modal-overlay: rgba(0, 0, 0, 0.7);
  --color-modal-border: #333333;
  
  /* Status Colors (slightly adjusted for dark theme) */
  --color-success: #48BB78;
  --color-warning: #FFA500;
  --color-error: #F56565;
  --color-info: #4299E1;

  /* Status Color Variants for Dark Theme */
  --color-success-text: #48BB78;
  --color-success-bg: rgba(72, 187, 120, 0.15);
  --color-error-text: #F56565;
  --color-error-bg: rgba(245, 101, 101, 0.15);
  --color-warning-text: #FFA500;
  --color-warning-bg: rgba(255, 165, 0, 0.15);
  --color-info-text: #4299E1;
  --color-info-bg: rgba(66, 153, 225, 0.15);
  
  /* Timer Colors */
  --color-timer-bg: #1e1e1e;
  --color-timer-border: #333333;
  --color-timer-button-active: rgba(64, 64, 64, 1);
  --color-timer-button-outlined: rgba(30, 30, 30, 1);
  
  /* Scrollbar Colors */
  --color-scrollbar-track: #262626;
  --color-scrollbar-thumb: #444444;
  --color-scrollbar-thumb-hover: #555555;

  /* Folder/Item Count Colors */
  --color-item-count-bg: #2a2a2a;
  --color-item-count-text: #aaa;

  /* Table and Content Colors */
  --color-table-cell: #aaa;
  --color-table-border: #333333;
  --color-table-header-bg: #262626;
  --color-empty-state: #aaa;
  --color-loading-text: #aaa;

  /* Create/Action Button Colors */
  --color-create-btn-bg: #444444;
  --color-create-btn-hover: #555555;
  --color-action-hover: rgba(255, 255, 255, 0.05);
  --color-action-danger-hover: rgba(231, 76, 60, 0.2);

  /* Breadcrumb Colors */
  --color-breadcrumb-text: #cccccc;
  --color-breadcrumb-hover: #333333;
  --color-breadcrumb-current: #ffffff;
  --color-breadcrumb-truncated: #aaaaaa;
  --color-breadcrumb-truncated-bg: #333333;
  --color-breadcrumb-truncated-hover: #444444;
  --color-breadcrumb-separator: #aaaaaa;

  /* Delete/Danger Button Colors */
  --color-delete-btn-bg: #4a2626;
  --color-delete-btn-text: #ff6b6b;
  --color-delete-btn-hover: #5a2d2d;
  --color-delete-btn-disabled-bg: #333333;
  --color-delete-btn-disabled-text: #666;

  /* Export/Progress Modal Colors */
  --color-export-overlay: rgba(0, 0, 0, 0.8);
  --color-export-modal-bg: #1e1e1e;
  --color-export-spinner-track: #333333;
  --color-export-spinner-active: #E0E0E0;
  --color-export-title: #E0E0E0;
  --color-export-message: #cccccc;
  --color-export-details: #888888;

  /* Path Dropdown Colors */
  --color-path-dropdown-bg: #1e1e1e;
  --color-path-dropdown-border: #333333;
  --color-path-dropdown-shadow: rgba(0, 0, 0, 0.4);
  --color-path-dropdown-item: #cccccc;
  --color-path-dropdown-hover: #333333;
  --color-path-dropdown-hover-text: #ffffff;
  --color-path-dropdown-current: #E0E0E0;
  --color-path-dropdown-current-bg: #333333;

  /* Folder Action Colors */
  --color-folder-action-bg: #333333;
  --color-folder-action-hover: #444444;
  --color-folder-action-delete: #ff6b6b;
  --color-folder-action-delete-hover: #4a2626;

  /* Folder Notes Colors */
  --color-folder-notes-title: #ffffff;
  --color-folder-notes-desc: #cccccc;

  /* Modal Overlay and Content */
  --color-modal-overlay-alt: rgba(0, 0, 0, 0.7);
  --color-modal-content-bg: #1e1e1e;
  --color-modal-content-shadow: rgba(0, 0, 0, 0.4);
  --color-modal-title-alt: #ffffff;

  /* Button Ripple Effect */
  --color-btn-ripple: rgba(255, 255, 255, 0.1);

  /* Form Input Colors */
  --color-form-input-border: #333333;
  --color-form-input-text: #E0E0E0;

  /* Warning Colors */
  --color-warning-bg: #2a2416;
  --color-warning-border: #ffa000;

  /* Dashboard Colors */
  --color-dashboard-date: #cccccc;
  --color-dashboard-stat-label: #cccccc;
  --color-dashboard-stat-border: #333333;
  --color-dashboard-stat-shadow: rgba(0, 0, 0, 0.3);
  --color-dashboard-empty-bg: #262626;
  --color-dashboard-empty-text: #aaaaaa;
  --color-dashboard-chart-primary: #E0E0E0;
  --color-dashboard-chart-secondary: #cccccc;
  --color-dashboard-chart-tertiary: #aaaaaa;
  --color-dashboard-chart-grid: #333333;
  --color-dashboard-action-icon-bg: #333333;
  --color-dashboard-action-icon-border: #444444;

  /* SVG Icon Colors */
  --color-icon-fill: #E0E0E0;
  --icon-filter: brightness(0) saturate(100%) invert(90%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);

  /* Trash Button Colors */
  --color-trash-btn-bg: #4a2626;
  --color-trash-btn-hover: #5a2d2d;
  --color-trash-btn-disabled-bg: #333333;

  /* Export Progress Modal Colors */
  --color-export-progress-overlay: rgba(0, 0, 0, 0.8);
  --color-export-progress-spinner-track: #333333;
  --color-export-progress-spinner-active: #E0E0E0;
  --color-export-progress-title: #E0E0E0;
  --color-export-progress-message: #cccccc;
  --color-export-progress-details: #888888;

  /* Window Control Colors */
  --color-window-close-hover: #e81123;
  --color-window-close-text: #ffffff;

  /* Wave Animation Colors */
  --color-wave-primary: rgba(224, 224, 224, 0.4);
  --color-wave-secondary: rgba(224, 224, 224, 0.7);
  --color-wave-tertiary: rgba(224, 224, 224, 0.3);

  /* Chart Fallback Colors */
  --color-chart-primary-fallback: rgba(224, 224, 224, 0.8);
  --color-chart-primary-border-fallback: rgba(224, 224, 224, 1);
  --color-chart-secondary-fallback: rgba(170, 170, 170, 0.6);
  --color-chart-secondary-border-fallback: rgba(170, 170, 170, 1);
  --color-chart-text-fallback: #E0E0E0;
  --color-chart-tertiary-text-fallback: #aaa;
  --color-chart-tooltip-bg-fallback: rgba(30, 30, 30, 0.95);
  --color-chart-tooltip-text-fallback: #E0E0E0;
  --color-chart-border-fallback: rgba(68, 68, 68, 0.3);

  /* Single Folder Component Colors */
  --color-folder-active-bg: #2a3441;
  --color-folder-hover-bg: #333333;
  --color-folder-breadcrumb-bg: #2a2a2a;
  --color-folder-breadcrumb-text: #6bb6ff;
  --color-folder-breadcrumb-current: #ffffff;
  --color-folder-breadcrumb-truncated: #aaaaaa;
  --color-folder-breadcrumb-hover: #444444;
  --color-folder-breadcrumb-hover-alt: #555555;
  --color-folder-breadcrumb-separator: #aaaaaa;
  --color-folder-rename-border: #6bb6ff;
  --color-folder-rename-bg: #1e1e1e;
  --color-folder-count-bg: #333333;
  --color-folder-count-text: #cccccc;
}

/* ===== FUTURE THEMES ===== */
/*
 * Additional themes can be easily added here by copying the light theme structure
 * and adjusting the color values. Examples:
 *
 * .theme-blue { ... }
 * .theme-green { ... }
 * .theme-purple { ... }
 * .theme-high-contrast { ... }
 *
 * Each theme should define all 200+ CSS variables for complete coverage.
 */

/* ===== THEME TRANSITIONS ===== */
/*
 * Theme transitions removed for instant switching per user preference.
 * User prefers instant animations (no transitions) for theme changes
 * to match the overall application animation philosophy.
 */

/* ===== GLOBAL SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

::-webkit-scrollbar-button {
  display: none;
}

::-webkit-scrollbar-corner {
  background: var(--color-scrollbar-track);
}

/* ===== PROSEMIRROR SPECIFIC SCROLLBAR STYLES ===== */
/* These styles specifically target ProseMirror elements to ensure custom scrollbars work */
.ProseMirror::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ProseMirror::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.ProseMirror::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.ProseMirror::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.ProseMirror::-webkit-scrollbar-button {
  display: none;
}

.ProseMirror::-webkit-scrollbar-corner {
  background: var(--color-scrollbar-track);
}
