.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 650px;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: calc(100vh - 40px);
  min-height: 300px;
}

.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-buttons {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--color-modal-border);
  background-color: var(--color-bg-secondary);
  flex-shrink: 0;
}

.tab-button {
  flex: 1;
  padding: 16px;
  background: none;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab-button.active {
  color: var(--color-text-primary);
  background-color: var(--color-modal-bg);
  border-bottom-color: var(--color-primary);
}

.tab-button:hover:not(.active) {
  background-color: var(--color-nav-item-hover);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
  scrollbar-gutter: stable;
}

.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.tab-content.notes-tab {
  overflow-y: auto;
}

/* Details Tab Styles */
.form-container {
  padding: 16px;
  flex: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.form-content {
  border: 1px solid var(--color-card-border);
  border-radius: 10px;
  padding: 15px;
  position: relative;
  background-color: var(--color-bg-tertiary);
  width: 550px;
  margin: 0 auto;
  box-sizing: border-box;
}

.book-info-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
  margin-bottom: 0;
}

.main-info-row {
  display: flex;
  gap: 20px;
  width: 100%;
  align-items: stretch;
}

.cover-column {
  width: 135px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.main-fields-column {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-top: 15px;
}

.cover-display {
  width: 135px;
  height: 192px;
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-display.editable {
  cursor: pointer;
  transition: all 0.2s;
}

.cover-display.editable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--color-card-shadow);
}

.cover-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 1;
}

.cover-display.editable:hover .cover-upload-overlay {
  opacity: 1;
}

.upload-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  padding: 8px;
  line-height: 1.2;
}

.cover-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg-tertiary);
}

/* Fixed form field styles for better alignment */
.form-field {
  display: flex;
  flex-direction: row;
  align-items: center;
  /* Changed from flex-start to center for better alignment */
  flex: 1;
  min-width: 0;
  gap: 10px;
  min-height: 40px;
  /* Added consistent minimum height */
}

/* Edit mode makes fields vertical */
.form-field.edit-mode {
  flex-direction: column;
  align-items: stretch;
  gap: 3px;
  min-height: auto;
  /* Reset min-height for edit mode */
}

.form-field.full-width {
  width: 100%;
}

/* Special styling for title and author fields */
.form-field.title-field,
.form-field.author-field {
  flex-direction: column;
  align-items: stretch;
  gap: 3px;
  min-height: auto;
  /* Reset min-height for these special fields */
}

.form-field>label {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
  min-width: 130px;
  text-align: left;
  flex-shrink: 0;
  line-height: 1.2;
  /* Added for consistent line height */
}

/* In edit mode, labels are full width */
.form-field.edit-mode>label {
  min-width: auto;
  width: 100%;
  margin-bottom: 3px;
}

.form-field.description-field {
  flex-direction: column;
  align-items: stretch;
  min-height: auto;
  /* Reset min-height for description field */
}

.form-field.description-field>label {
  width: auto;
  min-width: 0;
  margin-bottom: 6px;
}

/* Field display styles - Fixed alignment and overflow */
.field-display {
  font-size: 14px;
  color: var(--color-text-primary);
  min-height: 32px;
  box-sizing: border-box;
  padding: 6px 0;
  flex: 1;
  background-color: transparent;
  border: none;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  line-height: 1.2;
  /* Added for consistent line height */
  max-width: 100%;
  /* Added to prevent overflow */
}

.field-display.title-display {
  font-size: 18px;
  font-weight: 600;
  min-height: 36px;
}

.field-display.author-display {
  font-size: 16px;
  min-height: 34px;
  color: var(--color-text-secondary);
}

/* Fixed wrappable text to handle overflow properly */
.field-display.wrappable-text {
  white-space: normal;
  overflow: hidden;
  /* Changed from visible to hidden */
  text-overflow: clip;
  min-height: 32px;
  line-height: 1.4;
  align-items: flex-start;
  padding-top: 8px;
  word-wrap: break-word;
  /* Added to break long words */
  word-break: break-word;
  /* Added for better word breaking */
  max-width: 100%;
  /* Ensure it doesn't exceed container width */
}

/* Field content wrapper - Added max-width constraint */
.field-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  max-width: 100%;
  /* Added to prevent overflow */
  overflow: hidden;
  /* Added to contain overflow */
}

/* Input styling */
.form-field input,
.form-field textarea {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  outline: none;
  transition: all 0.2s;
  background-color: var(--color-input-bg);
  width: 100%;
  box-sizing: border-box;
  height: 32px;
}

/* Special styling for title and author inputs */
.title-input {
  font-size: 18px;
  font-weight: 600;
  height: 36px;
}

.author-input {
  font-size: 16px;
  height: 34px;
}

.form-field input:focus,
.form-field textarea:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.1);
}

.field-display.description {
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 8px 12px;
  background-color: var(--color-bg-secondary);
  height: 130px;
  min-height: 130px;
  max-height: 130px;
  overflow-y: auto;
  line-height: 1.4;
  width: 100%;
  box-sizing: border-box;
  white-space: normal;
  text-overflow: clip;
  align-items: flex-start;
}

.field-display.description::-webkit-scrollbar {
  width: 8px;
}

.field-display.description::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.field-display.description::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.field-display.description::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Textarea styling */
.form-field.description-field textarea {
  resize: none;
  height: 130px;
  min-height: 130px;
  line-height: 1.4;
  padding: 8px 12px;
  overflow-y: auto;
}

.form-field.description-field textarea::-webkit-scrollbar {
  width: 8px;
}

.form-field.description-field textarea::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.form-field.description-field textarea::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.form-field.description-field textarea::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.form-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.rating-container {
  display: flex;
  align-items: center;
}

.rating-stars {
  display: flex;
  gap: 12px;
  padding: 2px 0;
}

.star {
  cursor: pointer;
  transition: all 0.2s;
  width: 20px;
  height: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.star:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
}

/* Error styling */
.input-error {
  border-color: var(--color-error) !important;
  background-color: rgba(229, 57, 53, 0.05) !important;
}

.error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-top: 4px;
  display: block;
  width: 100%;
}

.section-divider {
  width: 100%;
  border: none;
  border-top: 1px solid var(--color-border-primary);
  margin: 12px 0;
}

/* Notes Tab Styles */
.notes-tab {
  display: flex;
  flex-direction: column;
}

.notes-header {
  padding: 16px 0 10px 0;
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
  width: 550px;
  margin: 0 auto;
  box-sizing: border-box;
}

.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  opacity: 0.5;
  top: 50%;
  transform: translateY(-50%);
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 12px 0 36px;
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  background-color: var(--color-input-bg);
  color: var(--color-input-text);
}

.search-input:focus {
  border-color: var(--color-input-focus);
}

.new-note-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 12px;
  height: 40px;
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
  box-sizing: border-box;
}

.new-note-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.new-note-button img {
  width: 14px;
  height: 14px;
}

.notes-container {
  padding: 0 16px 16px 16px;
  flex: 1;
  box-sizing: border-box;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.notes-content {
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 10px;
  position: relative;
  background-color: var(--color-bg-secondary);
  width: 550px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  scrollbar-gutter: stable;
}

.notes-content::-webkit-scrollbar {
  width: 8px;
}

.notes-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.notes-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.notes-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.notes-list {
  flex: 1;
  padding: 5px;
}

.no-notes-container {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 0;
}

.no-notes-message {
  margin-bottom: 15px;
}

.empty-notes-state {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 40px 20px;
}

.empty-notes-state p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.empty-notes-state p:last-child {
  margin-bottom: 0;
  font-weight: 500;
  color: var(--color-text-primary);
}

/* Note Card Styling */
.note-card {
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease, border-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  position: relative;
  user-select: none;
  background-color: var(--color-card-bg);
  margin-bottom: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-card-border);
  box-shadow: 0 1px 3px var(--color-card-shadow);
}

.note-card:first-child {
  margin-top: 0;
}

.note-card:last-child {
  margin-bottom: 0;
}

.note-card:hover {
  background-color: var(--color-nav-item-hover);
  border-color: var(--color-border-hover);
  box-shadow: 0 2px 8px var(--color-card-hover-shadow);
}

.note-card-content {
  width: 100%;
  flex: 1;
  padding-left: 0;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Montserrat', sans-serif;
}

.note-excerpt {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  line-height: 1.4;
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-muted);
  width: 100%;
  font-family: 'Montserrat', sans-serif;
}

.note-date {
  font-weight: 500;
  color: var(--color-text-tertiary);
}

.note-path {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  color: var(--color-text-muted);
  font-style: italic;
}

.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-modal-border);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  background-color: var(--color-modal-bg);
}

.btn {
  padding: 10px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-error);
}

.btn-danger:hover {
  background-color: var(--color-error);
  border-color: var(--color-error);
  opacity: 0.8;
}

.spacer {
  flex-grow: 1;
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    max-height: calc(100vh - 20px);
  }

  .modal-header {
    padding: 12px 20px;
  }

  .modal-header h3 {
    font-size: 24px;
    max-width: 250px;
  }

  .header-buttons {
    top: 16px;
    right: 16px;
  }

  .modal-footer {
    padding: 12px 20px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .btn {
    min-width: 100px;
    padding: 8px 16px;
    font-size: 14px;
  }

  .form-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-header {
    width: calc(100vw - 72px);
    max-width: 550px;
  }
}

@media (max-width: 640px) {
  .main-info-row {
    flex-direction: column;
    gap: 16px;
  }

  .cover-column {
    width: 100%;
  }

  .cover-display {
    width: 100%;
    height: 250px;
    margin: 0 auto;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .notes-header {
    flex-direction: column;
    gap: 12px;
  }

  .new-note-button {
    width: 100%;
    justify-content: center;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .spacer {
    display: none;
  }

  .form-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-header {
    width: calc(100vw - 72px);
    max-width: 550px;
  }
}

@media (max-height: 600px) {
  .modal-content {
    max-height: calc(100vh - 20px);
  }

  .form-container {
    padding: 12px;
  }

  .notes-container {
    padding: 12px;
  }

  .cover-display {
    height: 150px;
  }

  .form-field.description-field textarea,
  .field-display.description {
    height: 80px;
    min-height: 80px;
    max-height: 80px;
  }
}

/* Title wrapping override - Allow long titles to wrap to multiple lines */
.field-display.title-display {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  align-items: flex-start !important;
  line-height: 1.3 !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  padding-top: 8px !important;
}
</style>