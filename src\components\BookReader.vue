<template>
  <div class="read-container" :class="{ 'expanded': isReaderExpanded }">
    <!-- Book Import Section (shown when no book file is imported) -->
    <div v-if="!hasBookFile" class="import-section">
      <div class="import-area" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
        <div class="import-icon">
          <img src="/icons/book-icon.svg" alt="Book" />
        </div>
        <h4>Import Book File</h4>
        <p>Drag and drop or click to select</p>
        <button class="import-button" @click="triggerFileInput">
          Choose File
        </button>
        <div class="supported-formats">
          Supports: EPUB, PDF, MOBI, AZW3, FB2, CBZ
        </div>
      </div>
      <input ref="bookFileInput" type="file"
             accept=".epub,.pdf,.mobi,.azw3,.fb2,.cbz"
             @change="handleBookImport" style="display: none;">
    </div>

    <!-- Book Reader Section (shown when book file is imported) -->
    <div v-else class="book-reader-container">
      <!-- Reading Controls Header -->
      <div class="reading-controls-header">
        <div class="navigation-controls">
          <button class="nav-control nav-control--prev" @click="previousPage" :disabled="currentPage <= 1" :class="{ 'nav-control--disabled': currentPage <= 1 }">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <div class="page-edit">
            <input
              ref="pageInput"
              type="number"
              :value="currentPage"
              @input="handlePageInput"
              @blur="handlePageEdit"
              @keyup.enter="handlePageEdit"
              :min="1"
              :max="totalPages"
              class="page-input"
            />
            <span class="page-total">/ {{ totalPages }}</span>
          </div>
          <button class="nav-control nav-control--next" @click="nextPage" :disabled="currentPage >= totalPages" :class="{ 'nav-control--disabled': currentPage >= totalPages }">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="reader-actions">
          <button class="reader-action-button" @click="toggleBookmarks" title="Bookmarks">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M3 2a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v12l-5-3-5 3V2z" fill="currentColor"/>
            </svg>
          </button>
          <button class="reader-action-button" @click="toggleSettings" title="Settings">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z" fill="currentColor"/>
              <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z" fill="currentColor"/>
            </svg>
          </button>
          <button class="reader-action-button expand-button" @click="toggleExpanded" :title="isReaderExpanded ? 'Compact View' : 'Expand Reader'">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" v-if="!isReaderExpanded">
              <path d="M1.5 1a.5.5 0 0 0-.5.5v4a.5.5 0 0 1-1 0v-4A1.5 1.5 0 0 1 1.5 0h4a.5.5 0 0 1 0 1h-4zM10 .5a.5.5 0 0 1 .5-.5h4A1.5 1.5 0 0 1 16 1.5v4a.5.5 0 0 1-1 0v-4a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 1-.5-.5zM.5 10a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h4a.5.5 0 0 1 0 1h-4A1.5 1.5 0 0 1 0 14.5v-4a.5.5 0 0 1 .5-.5zm15 0a.5.5 0 0 1 .5.5v4a1.5 1.5 0 0 1-1.5 1.5h-4a.5.5 0 0 1 0-1h4a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5z" fill="currentColor"/>
            </svg>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" v-else>
              <path d="M5.5 0a.5.5 0 0 1 .5.5v4A1.5 1.5 0 0 1 4.5 6h-4a.5.5 0 0 1 0-1h4a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5zm5 0a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h4a.5.5 0 0 1 0 1h-4A1.5 1.5 0 0 1 10 4.5v-4a.5.5 0 0 1 .5-.5zM0 10.5a.5.5 0 0 1 .5-.5h4A1.5 1.5 0 0 1 6 11.5v4a.5.5 0 0 1-1 0v-4a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 1-.5-.5zm10 1a1.5 1.5 0 0 1 1.5-1.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 0-.5.5v4a.5.5 0 0 1-1 0v-4z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Main Reading Area -->
      <div class="reading-area" ref="readingArea">
        <div class="book-content">
          <!-- Placeholder for book content -->
          <div class="content-placeholder">
            <h3>{{ book.title }}</h3>
            <p class="book-content-text">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
            </p>
            <p class="book-content-text">
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
            </p>
            <p class="book-content-text">
              Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.
            </p>
          </div>
        </div>
      </div>

      <!-- Reading Progress Bar -->
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref } from 'vue'
import type { BookWithNoteCount } from '../types/electron-api'

export default defineComponent({
  name: 'BookReader',
  props: {
    book: {
      type: Object as PropType<BookWithNoteCount>,
      required: true
    },
    expanded: {
      type: Boolean,
      default: false
    }
  },
  emits: ['toggle-expanded'],
  setup(props, { emit }) {
    // Read tab reactive variables
    const isReaderExpanded = ref(props.expanded)
    const hasBookFile = ref(false)
    const currentPage = ref(1)
    const totalPages = ref(100)
    const progressPercentage = ref(25)
    const bookFileInput = ref<HTMLInputElement | null>(null)
    const pageInput = ref<HTMLInputElement | null>(null)
    const readingArea = ref<HTMLElement | null>(null)

    // Read tab methods
    const toggleExpanded = () => {
      isReaderExpanded.value = !isReaderExpanded.value;
      emit('toggle-expanded', isReaderExpanded.value);
    };

    const triggerFileInput = () => {
      bookFileInput.value?.click();
    };

    const handleFileDrop = (event: DragEvent) => {
      event.preventDefault();
      const files = event.dataTransfer?.files;
      if (files && files.length > 0) {
        processBookFile(files[0]);
      }
    };

    const handleBookImport = (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        processBookFile(file);
      }
    };

    const processBookFile = (file: File) => {
      // Simulate file processing
      console.log('Processing book file:', file.name);
      hasBookFile.value = true;
      // Reset to first page when new book is loaded
      currentPage.value = 1;
      progressPercentage.value = 0;
    };

    const previousPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
        updateProgress();
      }
    };

    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
        updateProgress();
      }
    };

    const updateProgress = () => {
      progressPercentage.value = (currentPage.value / totalPages.value) * 100;
    };

    const toggleBookmarks = () => {
      console.log('Toggle bookmarks');
      // Placeholder for bookmarks functionality
    };

    const toggleSettings = () => {
      console.log('Toggle settings');
      // Placeholder for settings functionality
    };

    const handlePageInput = (event: Event) => {
      const input = event.target as HTMLInputElement;
      const newPage = parseInt(input.value);

      if (newPage >= 1 && newPage <= totalPages.value) {
        currentPage.value = newPage;
        updateProgress();
      }
    };

    const handlePageEdit = (event: Event) => {
      const input = event.target as HTMLInputElement;
      const newPage = parseInt(input.value);

      if (newPage >= 1 && newPage <= totalPages.value) {
        currentPage.value = newPage;
        updateProgress();
      } else {
        // Reset to current page if invalid
        input.value = currentPage.value.toString();
      }
    };

    return {
      // Reactive variables
      isReaderExpanded,
      hasBookFile,
      currentPage,
      totalPages,
      progressPercentage,
      bookFileInput,
      pageInput,
      readingArea,
      // Methods
      toggleExpanded,
      triggerFileInput,
      handleFileDrop,
      handleBookImport,
      previousPage,
      nextPage,
      toggleBookmarks,
      toggleSettings,
      handlePageInput,
      handlePageEdit
    }
  }
})
</script>

<style scoped>
/* Read Container Styles */
.read-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease-in-out;
}

/* Import Section Styles */
.import-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.import-area {
  text-align: center;
  padding: 40px;
  border: 2px dashed var(--color-border-primary);
  border-radius: 12px;
  background-color: var(--color-bg-secondary);
  transition: all 0.2s;
  cursor: pointer;
  max-width: 400px;
  width: 100%;
}

.import-area:hover {
  border-color: var(--color-primary);
  background-color: var(--color-bg-tertiary);
}

.import-icon {
  margin-bottom: 16px;
}

.import-icon img {
  width: 48px;
  height: 48px;
  opacity: 0.7;
}

.import-area h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.import-area p {
  margin: 0 0 20px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.import-button {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
  border-radius: 8px;
  padding: 12px 24px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 16px;
}

.import-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.supported-formats {
  font-size: 12px;
  color: var(--color-text-tertiary);
  margin-top: 12px;
}

/* Book Reader Styles */
.book-reader-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.reading-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-secondary);
  flex-shrink: 0;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-control {
  width: 32px;
  height: 32px;
  background: var(--color-btn-secondary-bg);
  border: 1px solid var(--color-btn-secondary-border);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.nav-control:hover:not(.nav-control--disabled) {
  background: var(--color-btn-secondary-hover);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
}

.nav-control:active:not(.nav-control--disabled) {
  transform: scale(0.95);
}

.nav-control--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-edit {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
  justify-content: center;
}

.page-input {
  width: 50px;
  padding: 4px 6px;
  border: 1px solid var(--color-border-primary);
  border-radius: 4px;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  font-family: inherit;
  transition: border-color 0.2s;
}

.page-input:focus {
  outline: none;
  border-color: var(--color-primary);
}

.page-total {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.reader-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reader-action-button {
  width: 32px;
  height: 32px;
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
}

.reader-action-button:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.reader-action-button:active {
  transform: scale(0.95);
}

.reader-action-button svg {
  width: 16px;
  height: 16px;
}

/* Reading Area Styles */
.reading-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--color-bg-primary);
  min-height: 0;
}

.reading-area::-webkit-scrollbar {
  width: 8px;
}

.reading-area::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.reading-area::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.reading-area::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.book-content {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.content-placeholder h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 20px 0;
  text-align: center;
}

.book-content-text {
  font-size: 16px;
  color: var(--color-text-primary);
  margin: 0 0 16px 0;
  text-align: justify;
  font-family: 'Georgia', serif;
}

/* Progress Bar Styles */
.progress-bar {
  height: 4px;
  background-color: var(--color-bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 16px 16px 16px;
  flex-shrink: 0;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .reading-controls-header {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .navigation-controls {
    gap: 8px;
  }

  .page-input {
    width: 40px;
    font-size: 12px;
  }

  .page-total {
    font-size: 12px;
  }

  .reader-action-button {
    width: 28px;
    height: 28px;
  }

  .reader-action-button svg {
    width: 14px;
    height: 14px;
  }

  .book-content-text {
    font-size: 18px;
  }
}

@media (max-width: 640px) {
  .reading-controls-header {
    padding: 12px;
  }

  .navigation-controls {
    gap: 8px;
  }

  .page-input {
    width: 35px;
    font-size: 11px;
  }

  .page-total {
    font-size: 11px;
  }

  .reader-action-button {
    width: 26px;
    height: 26px;
  }

  .reader-action-button svg {
    width: 12px;
    height: 12px;
  }

  .page-total {
    font-size: 12px;
  }

  .reading-area {
    padding: 16px;
  }

  .book-content-text {
    font-size: 14px;
  }
}
</style>
