# Book Reading Implementation Plan

## Overview
This document outlines the implementation plan for adding book reading functionality to the Noti application. The reading feature will be integrated into the existing `BookDetailsModal.vue` as a third tab called "Read", allowing users to import and read various book formats directly within the modal interface.

## Current State Analysis

### Existing BookDetailsModal Structure
- **Current Tabs**: Details, Notes
- **Modal Size**: 650px width, flexible height with max-height constraints
- **Tab System**: Reactive tab switching with proper state management
- **File Handling**: Already has cover image upload functionality
- **Integration**: Connected to book database and sync system

### Current Book Management Features
- Book metadata storage and management
- OpenLibrary integration for book information
- Cover image handling and display
- Book organization and search
- Note-taking linked to books
- Sync system integration

## Implementation Strategy

### Phase 1: Core Infrastructure Setup

#### 1.1 Library Integration
**Primary Library**: `foliate-js` (Recommended)
- **Installation**: `npm install foliate-js`
- **Supported Formats**: EPUB, MOBI, KF8 (AZW3), FB2, CBZ, PDF (experimental)
- **Benefits**: Comprehensive format support, modern ES modules, excellent performance

**Alternative Libraries** (if needed):
- `epub.js` for EPUB-only support
- `pdf.js` for PDF-specific functionality

#### 1.2 Database Schema Extensions
**New Fields for Books Table**:
```sql
ALTER TABLE books ADD COLUMN book_file_path TEXT;
ALTER TABLE books ADD COLUMN book_file_format TEXT;
ALTER TABLE books ADD COLUMN reading_progress REAL DEFAULT 0.0;
ALTER TABLE books ADD COLUMN last_read_position TEXT; -- CFI or page reference
ALTER TABLE books ADD COLUMN bookmarks TEXT; -- JSON array of bookmarks
ALTER TABLE books ADD COLUMN reading_settings TEXT; -- JSON object for user preferences
```

#### 1.3 File Storage Structure
```
Books/
├── [Book Title]/
│   ├── book-file.[epub|pdf|mobi|etc] (imported book file)
│   ├── .book-meta.json (existing metadata)
│   ├── .cover.jpg (existing cover)
│   ├── .reading-data.json (reading progress, bookmarks)
│   └── [existing note folders]
```

### Phase 2: BookDetailsModal Enhancement

#### 2.1 Tab System Extension
**Current Structure**:
```typescript
const activeTab = ref<'details' | 'notes'>('details')
```

**Enhanced Structure**:
```typescript
const activeTab = ref<'details' | 'notes' | 'read'>('details')
```

#### 2.2 Read Tab Implementation
**Components to Add**:
1. **BookImporter Component**: File upload/drag-drop interface
2. **BookReader Component**: Main reading interface using foliate-js
3. **ReadingControls Component**: Navigation, zoom, settings
4. **BookmarkPanel Component**: Bookmark management

#### 2.3 Modal Sizing Strategy
**Default State** (Read Tab):
- Maintain current modal size (650px width)
- Show single page/section of book content
- Compact reading controls

**Expanded State** (Read Tab):
- **Width**: Expand to ~90% of viewport width (max 1400px)
- **Height**: Expand to ~90% of viewport height
- **Positioning**: Center in viewport
- **Preserved Elements**: Timer, title bar, tab navigation
- **Toggle Button**: "Expand Reader" / "Compact View"

### Phase 3: File Import System

#### 3.1 Import Interface Design
**Location**: Read tab content area (when no book is imported)
```vue
<div class="import-section">
  <div class="import-area" @drop="handleFileDrop" @dragover.prevent>
    <div class="import-icon">📚</div>
    <h4>Import Book File</h4>
    <p>Drag and drop or click to select</p>
    <button class="import-button" @click="triggerFileInput">
      Choose File
    </button>
    <div class="supported-formats">
      Supports: EPUB, PDF, MOBI, AZW3, FB2, CBZ
    </div>
  </div>
  <input ref="bookFileInput" type="file" 
         accept=".epub,.pdf,.mobi,.azw3,.fb2,.cbz" 
         @change="handleBookImport" style="display: none;">
</div>
```

#### 3.2 File Processing Pipeline
1. **File Validation**: Check format and file integrity
2. **Storage**: Save to book's sync directory
3. **Metadata Extraction**: Extract book info if available
4. **Database Update**: Store file path and format
5. **Reader Initialization**: Load book into foliate-js

### Phase 4: Reading Interface Implementation

#### 4.1 BookReader Component Structure
```vue
<template>
  <div class="book-reader-container" :class="{ 'expanded': isExpanded }">
    <!-- Reading Controls Header -->
    <div class="reading-controls-header">
      <div class="navigation-controls">
        <button @click="previousPage">←</button>
        <span class="page-info">{{ currentPage }} / {{ totalPages }}</span>
        <button @click="nextPage">→</button>
      </div>
      <div class="reader-actions">
        <button @click="toggleBookmarks">🔖</button>
        <button @click="toggleSettings">⚙️</button>
        <button @click="toggleExpanded">
          {{ isExpanded ? '🗗' : '🗖' }}
        </button>
      </div>
    </div>
    
    <!-- Main Reading Area -->
    <div class="reading-area" ref="readingArea">
      <foliate-view ref="foliateView" @relocate="handleLocationChange"></foliate-view>
    </div>
    
    <!-- Reading Progress Bar -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
    </div>
  </div>
</template>
```

#### 4.2 Reading Features
**Core Features**:
- Page navigation (previous/next)
- Reading progress tracking
- Bookmark management
- Text search within book
- Font size adjustment
- Theme switching (light/dark)

**Advanced Features** (Future):
- Highlighting and annotations
- Text-to-speech
- Dictionary lookup
- Note-taking integration

### Phase 5: State Management and Persistence

#### 5.1 Reading State Management
```typescript
interface ReadingState {
  bookId: number;
  currentPosition: string; // CFI or page reference
  progressPercentage: number;
  bookmarks: Bookmark[];
  readingSettings: ReadingSettings;
  lastReadAt: string;
}

interface Bookmark {
  id: string;
  position: string;
  title: string;
  note?: string;
  createdAt: string;
}

interface ReadingSettings {
  fontSize: number;
  fontFamily: string;
  theme: 'light' | 'dark' | 'sepia';
  lineHeight: number;
  pageWidth: number;
}
```

#### 5.2 Auto-Save Functionality
- Save reading progress every 30 seconds
- Save on page turn
- Save on modal close
- Sync reading data across devices

### Phase 6: Integration Points

#### 6.1 IPC Handlers (Electron Main Process)
```typescript
// New IPC handlers to add
ipcMain.handle('books:importFile', async (event, bookId, filePath) => {
  // Handle book file import
});

ipcMain.handle('books:getReadingData', async (event, bookId) => {
  // Get reading progress and bookmarks
});

ipcMain.handle('books:saveReadingData', async (event, bookId, readingData) => {
  // Save reading progress and bookmarks
});

ipcMain.handle('books:openReader', async (event, bookId) => {
  // Initialize book reader
});
```

#### 6.2 Sync System Integration
- Include book files in sync manifest
- Handle book file synchronization
- Preserve reading progress across devices
- Manage bookmark synchronization

### Phase 7: UI/UX Considerations

#### 7.1 Modal Expansion Animation
```css
.modal-content.expanded {
  width: 90vw;
  height: 90vh;
  max-width: 1400px;
  max-height: 900px;
  transition: all 0.3s ease-in-out;
}

.book-reader-container.expanded {
  height: calc(100% - 120px); /* Account for header and controls */
}
```

#### 7.2 Responsive Design
- **Desktop**: Full featured reader with all controls
- **Tablet**: Optimized touch controls
- **Mobile**: Simplified interface, swipe navigation

#### 7.3 Accessibility
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Adjustable font sizes

## Implementation Timeline

### Week 1-2: Infrastructure Setup
- Install and configure foliate-js
- Extend database schema
- Set up file storage structure
- Create basic import interface

### Week 3-4: Core Reading Functionality
- Implement BookReader component
- Add basic navigation controls
- Implement file import pipeline
- Add reading progress tracking

### Week 5-6: Modal Integration
- Integrate reader into BookDetailsModal
- Implement modal expansion functionality
- Add reading controls and settings
- Test with various book formats

### Week 7-8: Advanced Features
- Add bookmark system
- Implement search functionality
- Add reading settings panel
- Integrate with sync system

### Week 9-10: Polish and Testing
- UI/UX refinements
- Performance optimization
- Cross-platform testing
- Documentation updates

## Technical Considerations

### Performance Optimization
- Lazy load book content
- Implement virtual scrolling for large books
- Cache frequently accessed pages
- Optimize memory usage for large files

### Error Handling
- Graceful handling of corrupted book files
- Fallback for unsupported formats
- Network error handling for sync
- User-friendly error messages

### Security Considerations
- Validate uploaded book files
- Sanitize book content
- Prevent XSS attacks from book content
- Secure file storage and access

## Success Metrics

### Functional Requirements
- [ ] Successfully import and display EPUB files
- [ ] Successfully import and display PDF files
- [ ] Reading progress persistence
- [ ] Modal expansion/contraction
- [ ] Cross-device synchronization

### Performance Requirements
- Book loading time < 3 seconds
- Page navigation response < 500ms
- Modal expansion animation < 300ms
- Memory usage < 200MB for typical books

### User Experience Requirements
- Intuitive import process
- Smooth reading experience
- Consistent with app design language
- Accessible to users with disabilities

## Future Enhancements

### Phase 2 Features
- Advanced annotation system
- Integration with note-taking
- Social reading features (sharing quotes)
- Reading statistics and analytics

### Phase 3 Features
- AI-powered book summaries
- Automatic chapter detection
- Reading recommendations
- Export reading notes to various formats

This implementation plan provides a comprehensive roadmap for adding robust book reading functionality to the Noti application while maintaining the existing user experience and architectural patterns.
